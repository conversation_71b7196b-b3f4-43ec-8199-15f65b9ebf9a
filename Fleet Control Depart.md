# GoMyHire 司机运营管理规范

## 目录

*   [1. 司机运营管理规范目的](#1)
*   [2. 适用范围](#2)
*   [3. 规范内容](#3)
*   [4. 规范流程](#4)
*   [5. 规范责任](#5)
*   [6. 规范评估](#6)
*   [7. 规范修订](#7)
*   [8. 服务时效管理](#8)
*   [9. 司机工作流程](#9)

## <a name="1"></a>1. 司机运营管理规范目的

*   确保司机运营管理工作的标准化、规范化和高效化
*   提高司机运营管理工作的质量和效率
*   保障司机运营管理工作的安全性和合规性

## <a name="2"></a>2. 适用范围

*   本规范适用于GoMyHire平台的司机运营管理工作
*   本规范适用于GoMyHire平台的所有司机运营管理人员

## <a name="3"></a>3. 规范内容

### 3.1 司机运营管理流程

*   司机注册
*   司机审核
*   司机激活
*   司机管理
*   司机评价

### 3.2 司机运营管理标准

*   司机注册标准
*   司机审核标准
*   司机激活标准
*   司机管理标准
*   司机评价标准

## <a name="4"></a>4. 规范流程

### 4.1 司机运营管理流程图

```mermaid
graph TD
    %% 主流程
    A[司机注册] --> B[司机审核资料]
    B --> C[司机考试]
    C --> D[司机审核通过]
    D --> E[司机激活]
    E --> F[司机管理]
    F --> G[司机评价]
    G --> F
    
    %% 司机注册分支
    A --> A1[个人信息提交]
    A --> A2[车辆信息提交]
    A --> A3[驾驶证验证]
    A --> A4[背景调查]
    A1 & A2 & A3 & A4 --> A5{信息完整?}
    A5 -->|否| A6[退回修改]
    A6 --> A
    A5 -->|是| B
    
    %% 司机审核分支
    B --> B1[身份验证]
    B --> B2[驾驶资质验证]
    B --> B3[车辆资质验证]
    B --> B4[保险验证]
    B1 & B2 & B3 & B4 --> B5{审核通过?}
    B5 -->|否| B6[退回修改]
    B6 --> A
    B5 -->|是| C
    
    %% 司机考试分支
    C --> C1[交通规则测试]
    C --> C2[平台规则测试]
    C --> C3[服务标准测试]
    C --> C4[实际驾驶技能测试]
    C1 & C2 & C3 & C4 --> C5{考试通过?}
    C5 -->|否| C6[重考]
    C6 --> C
    C5 -->|是| D
    
    %% 司机激活分支
    E --> E1[平台账号激活]
    E --> E2[APP使用培训]
    E --> E3[服务标准培训]
    E --> E4[初始评级设置]
    E1 & E2 & E3 & E4 --> E5[试用期]
    E5 --> F
    
    %% 司机管理分支
    F --> F1[日常考勤管理]
    F --> F2[订单分配管理]
    F --> F3[服务质量监控]
    F --> F4[投诉处理]
    F --> F5[奖惩管理]
    F --> F6[培训提升]
    F1 & F2 & F3 & F4 & F5 & F6 --> F7[分级管理]
    F7 --> G
    
    %% 司机退出机制
    F --> H{违规情况?}
    H -->|严重违规| I[清退]
    H -->|主动申请| J[退出流程]
    I & J --> K[账号关闭]
```

### 4.2 司机运营管理流程说明

#### 4.2.1 主流程

* **司机注册**：司机提交注册申请，包括个人信息、车辆信息、驾驶证信息等
* **司机审核资料**：平台审核司机提交的各项资料，确保信息真实有效
* **司机考试**：司机参加平台组织的各项测试，确保具备必要的知识和技能
* **司机审核通过**：司机通过所有审核和考试，获得上岗资格
* **司机激活**：司机激活平台账号，接受必要培训，设置初始评级
* **司机管理**：平台对司机进行全方位管理，包括考勤、订单、服务质量等
* **司机评价**：对司机服务进行多维度评价，形成综合评分

#### 4.2.2 司机注册详细流程

* **个人信息提交**：包括身份证、联系方式、住址等基本信息
* **车辆信息提交**：包括车辆行驶证、车辆照片、车辆保险等
* **驾驶证验证**：验证驾驶证真实性、有效期、驾驶类型等
* **背景调查**：调查司机的驾驶记录、信用记录等背景信息
* **信息完整性检查**：检查所有提交信息是否完整，不完整则退回修改

#### 4.2.3 司机审核详细流程

* **身份验证**：验证司机身份信息的真实性和有效性
* **驾驶资质验证**：验证司机的驾驶资质是否符合平台要求
* **车辆资质验证**：验证车辆是否符合平台要求，包括车型、车龄等
* **保险验证**：验证司机和车辆的保险是否有效且符合要求
* **审核结果**：根据验证结果决定是否通过审核，不通过则退回修改

#### 4.2.4 司机考试详细流程

* **交通规则测试**：测试司机对交通规则的掌握程度
* **平台规则测试**：测试司机对平台规则和政策的了解程度
* **服务标准测试**：测试司机对服务标准和礼仪的掌握程度
* **实际驾驶技能测试**：测试司机的实际驾驶技能和应急处理能力
* **考试结果**：根据测试结果决定是否通过考试，不通过可重考最多2次，每次间隔不少于7天，3次仍不通过则需重新提交注册申请

#### 4.2.5 司机激活详细流程

* **平台账号激活**：激活司机在平台的账号，设置登录凭证
* **APP使用培训**：培训司机如何使用平台APP进行接单、导航等操作
* **服务标准培训**：培训司机如何提供符合平台标准的服务
* **初始评级设置**：根据司机的资质和表现设置初始评级
* **试用期管理**：在试用期内对司机进行重点监控和指导

#### 4.2.6 司机管理详细流程

* **日常考勤管理**：管理司机的上下线时间、工作时长等
* **订单分配管理**：根据司机的评级、位置等因素分配订单
* **服务质量监控**：监控司机的服务质量，包括准时率、完成率等
* **投诉处理**：处理乘客对司机的投诉，进行调查和处理
* **奖惩管理**：根据司机的表现实施奖励或处罚
* **培训提升**：为司机提供培训和提升机会，提高服务质量
* **分级管理**：根据司机的综合表现将司机分为不同等级，实施差异化管理

#### 4.2.7 司机评价详细流程

##### 4.2.7.1 乘客评价机制

* **评价收集方式**：
  * 订单完成后自动发送评价请求至乘客手机
  * 乘客可在APP内进行星级评分（1-5星）及文字评价
  * 评价窗口开放时间72小时

* **评价维度**：
  * 服务态度：司机的礼貌、耐心度和专业性
  * 驾驶技术：驾驶平稳性、安全性和路线选择
  * 车内环境：车辆清洁度、舒适度和设施完善性
  * 准时性：是否准时到达接送点
  * 特殊需求处理：对乘客特殊需求的响应和处理

* **评价权重**：
  * 乘客评价占总评分的50%
  * 低星评价（1-2星）将触发平台自动调查
  * 连续三次低星评价将导致司机账号暂停审核

##### 4.2.7.2 平台评价指标

* **数据指标监控**：
  * 订单完成率：成功完成的订单占接单总数的比例（目标值≥90%）
  * 准时率：准时到达的订单占总订单的比例（目标值≥95%）
  * 取消率：司机主动取消的订单比例（目标值≤5%）
  * 投诉率：收到投诉的订单占总订单的比例（目标值≤2%）
  * 在线响应时间：司机响应平台消息的平均时间（目标值≤3分钟）

* **安全驾驶监控**：
  * 速度超标次数：系统记录的超速驾驶次数（目标值≤0次）
  * 急加速/急制动次数：通过车载设备检测的急动作次数（目标值≤2次/小时）
  * 连续驾驶时间：无休息的最长驾驶时间（不应超过4小时）

* **服务标准遵守**：
  * 着装规范性：是否按要求着装并佩戴工作证件
  * 车辆清洁度：车辆内外清洁状况
  * 标准服务提供：是否提供矿泉水、WiFi等标准服务
  * 流程规范性：是否严格按照平台服务流程执行

* **平台评价权重**：
  * 平台评价占总评分的45%
  * 安全驾驶监控指标为一票否决指标，违反将直接导致评分下降

##### 4.2.7.3 自我评价机制

* **自我评价周期**：
  * 司机每月需完成一次自我评价
  * 平台提供自我评价模板和指导

* **自我评价内容**：
  * 服务优势与不足：识别自身的服务优势和需要改进的方面
  * 客户反馈分析：对收到的客户反馈进行分析和总结
  * 改进计划：提出具体的改进措施和目标
  * 培训需求：提出自身需要的培训支持

* **自我评价权重**：
  * 自我评价占总评分的5%
  * 自我评价需经过主管审核确认，确保客观性
  * 未完成自我评价将影响月度综合评分

##### 4.2.7.4 综合评分系统

* **评分计算方式**：
  * 总分100分，按权重分配：乘客评价(50%)+平台评价(45%)+自我评价(5%)
  * 安全违规将直接扣除固定分值，不计入权重计算（详见下方安全违规扣分标准）
  * 评分每月更新一次，并生成月度评价报告

* **评分级别划分**：
  | 级别 | 分数范围 | 等级描述 |
  |---------|------------|----------|
  | S级 | 95-100分 | 特级司机，享有最高订单分配优先权和奖励 |
  | A级 | 85-94分 | 优秀司机，享有订单分配优先权和奖励 |
  | B级 | 75-84分 | 良好司机，正常订单分配 |
  | C级 | 60-74分 | 待提升司机，订单量减少，需参加培训 |
  | D级 | 低于60分 | 警告级别，暂停接单资格，需整改 |

##### 4.2.7.5 评分结果应用

* **订单分配机制**：
  * S级司机优先获得高价值订单和特殊订单
  * A级司机优先获得优质订单
  * B级司机正常参与订单分配
  * C级司机订单量减少至少30%
  * D级司机暂停接单资格，需参加培训并整改

* **奖惩机制**：
  * S级司机：月度奖金、优先接单权、优先参与平台活动
  * A级司机：季度奖金、接单权益
  * C级司机：强制参加提升培训、订单量减少
  * D级司机：暂停接单、强制培训、连续两月D级可能导致清退

* **级别流动机制**：
  * 升级条件：连续两个月达到更高级别的评分标准
  * 降级条件：当月评分低于当前级别标准
  * 特殊情况：出现重大安全事故或严重投诉将直接降级

* **级别恢复机制**：
  * D级司机完成指定培训并通过考核后，可恢复至C级
  * 其他级别司机需通过正常评分提升机制恢复级别

##### 4.2.7.6 安全违规扣分标准

| 违规类型 | 违规行为 | 扣分标准 | 备注 |
|---------|---------|---------|------|
| 严重安全违规 | 酒后驾驶或服务期间饮酒 | 直接降至D级 | 情节严重者清退 |
| 严重安全违规 | 超速驾驶（超过限速30%以上） | 直接降至D级 | 情节严重者清退 |
| 严重安全违规 | 疯狂驾驶或故意危险驾驶 | 直接降至D级 | 情节严重者清退 |
| 一般安全违规 | 超速驾驶（超过限速10%-30%） | 扣除20分 | 一个月内累计两次降级 |
| 一般安全违规 | 闯红灯或不遵守交通信号 | 扣除15分 | 一个月内累计两次降级 |
| 一般安全违规 | 不系安全带或使用手机 | 扣除10分 | 一个月内累计三次降级 |
| 轻微安全违规 | 不按导航行驶且未告知乘客 | 扣除5分 | 一个月内累计三次降一级 |
| 轻微安全违规 | 车内设施不符合安全标准 | 扣除5分 | 限期整改，逾期未改降级 |

#### 4.2.8 司机退出机制

##### 4.2.8.1 违规清退标准

平台有权在以下情况下对司机进行清退：

* **安全违规**：
  * 酒后驾驶或服务期间饮酒
  * 超速驾驶（超过限速30%以上）
  * 疯狂驾驶或故意危险驾驶
  * 不遵守交通规则导致严重交通事故
  * 车辆安全设备不合规且拒不整改

* **服务违规**：
  * 在一个月内累计取消订单超过5次
  * 在三个月内收到客户严重投诉超过3次
  * 服务过程中对客人有辱骂、威胁或不当行为
  * 故意绕路或延长行程以增加费用
  * 服务完成率低于60%

* **诈骗行为**：
  * 提供虚假个人信息或车辆信息
  * 伪造服务证明或照片
  * 向客人收取未经授权的额外费用
  * 私自与客人进行交易绕过平台
  * 故意破坏或篡改平台计费系统

* **合规问题**：
  * 证件过期且拒不更新（驾照、车辆牌照、保险等）
  * 拒绝接受平台安全培训
  * 被发现有犯罪记录且未在注册时披露
  * 违反当地运营法规或导致平台受到监管处罚

* **系统滥用**：
  * 故意操纵平台系统或利用系统漏洞
  * 分享账号给未注册的人员使用
  * 利用多个账号进行运营套利
  * 未经授权使用平台数据或客户信息

##### 4.2.8.2 违规清退流程

* **警告机制**：
  * 除危及安全的紧急情况外，平台会先发出警告
  * 警告分为口头警告、书面警告和最终警告
  * 收到最终警告后仍未改正的，将启动清退程序

* **调查及证据收集**：
  * 平台将收集相关违规证据，包括客户投诉、系统记录、监控录像等
  * 通知司机提交说明或进行申辩
  * 组织专门团队进行评审

* **决定及通知**：
  * 评审团队作出清退决定
  * 书面通知司机清退原因及生效时间
  * 提供7天申诉期

* **账号处理**：
  * 冻结司机账号权限
  * 结算并支付剩余费用（扣除相关违约金）
  * 收回平台提供的设备或材料
  * 在系统中标记该司机不得再注册

* **主动退出**：司机可以主动申请退出平台，需提前15天提交申请

* **账号关闭**：无论何种方式退出，都需要完成账号关闭流程，确保信息安全

## <a name="5"></a>5. 规范责任

### 5.1 司机运营管理人员责任

* **规范执行责任**：
  * 严格按照本规范执行司机运营管理工作
  * 确保司机运营管理工作的标准化、规范化和高效化
  * 及时发现和解决司机运营管理工作中的问题
  * 对执行过程中的问题负直接责任，不得将执行责任转移给平台

* **司机招募与筛选责任**：
  * 负责司机招募计划的制定与执行
  * 严格按照司机审核标准筛选司机
  * 确保所有入驻司机符合平台要求和法律法规

* **司机培训责任**：
  * 组织新司机入职培训和考核
  * 定期组织在职司机的技能提升培训
  * 针对服务质量问题组织专项培训

* **司机管理责任**：
  * 日常监控司机的服务质量和表现
  * 处理司机投诉和乘客反馈
  * 执行司机奖惩机制
  * 维护司机评价体系的公正性和有效性

* **数据分析责任**：
  * 收集和分析司机运营数据
  * 提供司机运营报告和改进建议
  * 识别运营中的问题和优化机会

* **合规监督责任**：
  * 确保司机运营管理工作符合法律法规
  * 监督司机遵守平台规则和服务标准
  * 及时处理违规行为

### 5.2 平台责任

* **工具支持责任**：
  * 提供高效的司机管理系统和工具
  * 确保司机APP的稳定性和易用性
  * 定期优化和升级管理工具
  * 提供技术支持和问题解决

* **政策制定责任**：
  * 制定合理的司机运营政策
  * 根据市场变化和反馈调整政策
  * 确保政策的公平性和透明度
  * 及时向司机和管理人员传达政策变更
  * 对政策的合理性和有效性负责，并定期评估政策实施效果

* **资源保障责任**：
  * 提供必要的人力和物力资源
  * 保障司机运营管理工作的顺利开展
  * 提供必要的培训资源和材料

* **数据支持责任**：
  * 提供准确的数据分析工具
  * 保障数据的安全性和准确性
  * 提供决策支持数据

* **沟通协调责任**：
  * 建立畅通的沟通渠道
  * 协调各部门支持司机运营管理工作
  * 处理跨部门合作事项

* **风险管理责任**：
  * 识别和评估司机运营风险
  * 制定风险应对策略
  * 建立应急处理机制

### 5.3 责任协同机制

* **定期会议机制**：
  * 每周运营例会：讨论日常运营问题和解决方案
  * 月度战略会议：评估运营效果，调整运营策略
  * 季度全体会议：总结季度工作，规划下季度目标

* **问题上报机制**：
  * 建立明确的问题上报流程和渠道
  * 设定问题响应和解决时限
  * 跟踪问题解决进度和效果

* **绩效评估机制**：
  * 建立司机运营管理人员的绩效评估体系
  * 将规范执行情况纳入绩效考核
  * 根据绩效结果实施奖惩

* **知识共享机制**：
  * 建立司机运营管理知识库
  * 鼓励经验分享和最佳实践推广
  * 组织定期的经验交流活动

## <a name="6"></a>6. 规范评估

### 6.1 评估周期

* **季度评估**：
  * 重点评估运营流程执行效率
  * 分析司机服务质量趋势
  * 检查管理人员工作合规性
  
* **半年度评估**：
  * 评估规范与业务需求的匹配度
  * 分析政策调整的实际效果
  * 审查风险管理机制有效性

* **年度评估**：
  * 综合评估规范体系完整性
  * 对标行业最佳实践
  * 评估组织能力与资源配置

### 6.2 评估指标体系

#### 6.2.1 平台运营效率指标
| 指标类别 | 评估指标 | 目标值 | 数据来源 |
|----------|----------|--------|----------|
| 流程效率 | 司机审核通过率 | 75-85% | 审核系统 |
|          | 平均培训周期 | ≤3天 | 培训记录 |
|          | 问题解决响应时间 | ≤2小时 | 工单系统 |
| 资源效率 | 人车比匹配度 | 1:50 | 排班系统 |
|          | 工具使用率 | ≥90% | 日志分析 |

#### 6.2.2 司机服务质量指标
（引用第4.2.7章乘客评价指标与平台监控指标）

#### 6.2.3 管理效能指标
| 指标类型 | 评估维度 | 测量方式 |
|----------|----------|----------|
| 流程合规率 | 规范执行严格度 | 随机抽查结果 |
| 决策准确率 | 管理决策质量 | 历史决策回溯分析 |
| 问题复发率 | 改进措施有效性 | 问题跟踪系统 |

### 6.3 评估流程

```mermaid
graph TB
    A[启动评估] --> B[数据采集阶段]
    B --> C[分析诊断阶段]
    C --> D[改进规划阶段]
    
    B --> B1[系统日志采集]
    B --> B2[人工台账核查]
    B --> B3[司机满意度调查]
    
    C --> C1[数据可视化分析]
    C --> C2[根因分析]
    C --> C3[标杆对比]
    
    D --> D1[制定改进路线图]
    D --> D2[资源需求评估]
    D --> D3[修订规范草案]
```

### 6.4 评估结果应用

* **资源再分配**：
  * 根据评估结果调整区域资源配给
  * 优化管理人员工作负荷分配
  
* **政策调优**：
  * 修订矛盾条款（参见第7章修订机制）
  * 完善流程漏洞
  
* **能力建设**：
  * 识别管理人员技能缺口
  * 制定针对性培训计划

### 6.5 持续改进机制

* **PDCA循环**：
  * 计划(Plan)：基于评估结果制定改进计划
  * 执行(Do)：试点改进措施
  * 检查(Check)：监控改进效果
  * 处理(Act)：标准化成功经验

* **知识管理**：
  * 建立评估案例库
  * 开发常见问题解决方案模板
  * 形成最佳实践手册

* **版本控制**：
  * 记录规范版本与评估结果关联
  * 建立规范条款变更溯源机制

* **沟通机制**：
  * 向司机公开评估改进计划
  * 向管理人员反馈个人评估结果
  * 向股东报告战略级评估发现

## <a name="7"></a>7. 规范修订

*   根据司机运营管理工作的变化和发展，修订司机运营管理规范
*   修订后的规范须经相关部门审批后实施

## <a name="8"></a>8. 服务时效管理

### 8.1 等待超时费用体系

| 服务类型 | 免费等待时间 | 超时费率 | 特殊处理流程 | 凭证要求 |
|---------|--------------|----------|--------------|----------|
| 接机服务 | 90分钟 | 每30分钟+MYR30 | - 超时15分钟：系统自动提醒<br>- 超时30分钟：强制联系客服<br>- 超时60分钟：启动No-Show流程 | 需上传：<br>• 带时间戳的实时位置截图<br>• 车辆外观+周边环境照片<br>• 最后联系记录截屏 |
| 送机服务 | 30分钟 | 每30分钟+MYR30| - 超时5分钟：系统自动计算预估费用<br>- 超时15分钟：需二次确认乘客状态<br>- 超时30分钟：客服介入处理 | 需记录：<br>• 最后一次联系乘客时间<br>• 客服沟通记录截屏<br>• 车辆仪表盘时间特写 |
| 包车服务 | 按合同约定 | 每小时+MYR80<br>附加服务费需现场签订补充协议 | - 超时30分钟需现场签署延期确认书<br>- 每超1小时系统自动生成补充订单<br>- 超时需客户确认 | 需上传：<br>• 手写签字确认单照片<br>• 车载GPS轨迹记录<br>• 服务期间连续定位记录 |
| VIP接机 | 120分钟 | 超时收费期间MYR50/30min<br>(从司机到达机场至服务结束的整个过程)<br>含15分钟优先响应通道 | - 超时即触发管家服务介入<br>- 每15分钟向乘客发送进度更新<br>- 专属客服全程跟进 | 需上传：<br>• 贵宾接待区定位照片<br>• 举牌服务视频片段(至少10秒)<br>• 实时导航界面截图 |

### 8.2 超时费用执行细则

1. **费率时效**：本费率表有效期至2025年12月31日，后续按CPI指数每年调整
2. **争议处理**：费用争议需在48小时内通过[司机门户]-[订单争议]板块提交复核申请。特殊情况下超过48小时的争议，需提供充分证据并经管理层审批
3. **凭证时效**：所有证明需在订单结束后2小时内上传，逾期视为自动放弃费用追索权
4. **特殊情况**：遇恶劣天气、交通管制等不可抗力因素，需提供官方证明申请费用减免
5. **计费原则**：超时费用精确计算到分钟，不足30分钟按比例计费

### 8.3 超时处理流程

```mermaid
graph TD
    A[超时触发] --> B{服务类型判断}
    B -->|标准服务| C[系统自动计费]
    B -->|特殊服务| D[人工客服介入]
    C --> E((生成待确认账单))
    D --> F[三方沟通记录]
    E --> G[司机端推送确认]
    F --> G
    G --> H{乘客确认状态}
    H -->|已确认| I[计入订单费用]
    H -->|未响应| J[48小时后自动生效]
    I --> K[完成订单结算]
    J --> K
```

## <a name="9"></a>9. 司机工作流程

### 9.1 接单流程

#### 9.1.1 订单接收与评估

* **订单接收**：通过 GoMyHire Driver 应用接收订单通知
* **订单评估**：查看订单详情（时间、地点、乘客数量、行李情况）
* **接单确认**：点击“接受”按钮接受订单任务

#### 9.1.2 订单取消与误操作处理

* **取消流程**：需联系客服说明原因，由客服决定是否无责取消
* **误操作处理**：如误接订单，需立即联系客服说明情况，避免扣分
* **订单冲突处理**：发现与其他预定行程冲突时，尽早与客服说明冲突原因

### 9.2 联系客户流程

#### 9.2.1 客户沟通方式

* **初次联系**：通过应用内置的 Live Chat 功能（Talk to custom 按钮）联系乘客
* **信息确认**：确认乘客的具体位置、航班信息或特殊需求
* **语言障碍处理**：遇到语言沟通障碍时，可使用翻译软件辅助

#### 9.2.2 客户无响应处理

* **无响应处理**：若乘客无响应，需拍照上传位置证明并联系客服
* **航班延误处理**：向客服报告实际到达时间，拍照上传等待证明
* **等待超时处理**：提醒客服已接近免费等待极限，如超时需协商额外费用

### 9.3 服务执行流程

#### 9.3.1 服务开始与执行

* **出发确认**：点击“On the way”或“出发”按钮表示已前往接客点
* **到达通知**：到达接客点后通知乘客
* **举牌服务**：接机时如需举牌(Paging)，需准备纸质或电子名牌并拍照留证

#### 9.3.2 等待管理

* **接机服务等待**：免费等待时间 90 分钟，超时每 30 分钟加收 MYR30
* **送机服务等待**：免费等待时间 30 分钟，超时每 30 分钟加收 MYR30
* **超时几率证明**：需上传实时位置截图、车辆外观+周边环境照片

### 9.4 异常情况处理

#### 9.4.1 行程变更处理

* **小调整处理**：仅是上车地点或时间细节调整，可在Live Chat中说明
* **大变动处理**：涉及较大变动时，请让客服重新核算费用后再执行
* **临时更改下车点**：与乘客确认变更是否导致里程增加，可能需客服重新核价

#### 9.4.2 车辆与道路问题

* **车辆故障**：立即通知客服并提供 Order ID，拍照记录故障情况
* **道路封闭处理**：利用导航寻找替代路线，及时告知乘客并说明情况
* **恶劣天气处理**：降低行驶速度，保持安全距离，开启警示灯

#### 9.4.3 乘客相关问题

* **找不到乘客**：拍摄车辆所在位置（含车牌、GPS及时间），上传给客服
* **乘客投诉**：及时通知客服介入处理，避免与乘客发生争执
* **特殊乘客服务**：确认是否需要特殊设备，评估自身服务能力，优先确保安全

### 9.5 订单完成与扣分规则

#### 9.5.1 订单完成流程

* **完成确认**：服务完成后点击完成按钮
* **证明上传**：需要上传相关证明（如举牌照片）以获取额外费用
* **账单核对**：定期对比订单明细，确保收入准确

#### 9.5.2 扣分规则与避免

* **避免频繁取消订单**：系统检测到多次取消会自动扣分
* **提前报备可能迟到**：发现迟到情况时，立即在Live Chat或客服电话报备
* **按流程操作，保留证据**：所有异常情况都需保留证据（截图、照片、聊天记录）
* **误扣分申诉**：如被误扣分，及时向客服申诉

#### 9.5.3 扣分机制详细说明

##### 9.5.3.1 扣分触发情形

| 触发情形 | 说明 | 处理建议 |
|---------|------|----------|
| 频繁取消订单 | 短时间内多次取消订单，系统自动扣分 | 接单前仗细评估是否能完成，避免误接 |
| 服务迟到 | 未提前报备的迟到情况 | 发现可能迟到时立即报备，提供延误证据 |
| 不按流程操作 | 跳过必要的操作步骤或操作不规范 | 严格按照平台流程执行每一步操作 |
| 乘客投诉 | 因服务态度、车内环境等原因被乘客投诉 | 保持专业态度，确保车内清洁，提供优质服务 |
| 私自交易 | 与乘客进行平台外的私下交易 | 拒绝任何平台外交易，并向客服报备 |

##### 9.5.3.2 扣分等级与标准

| 扣分等级 | 影响范围 | 恢复周期 |
|---------|------|----------|
| 轻度扣分 | 影响订单分配优先级 | 7天内自动恢复 |
| 中度扣分 | 降低订单接收数量 | 15天内无新增扣分可申请恢复 |
| 重度扣分 | 可能导致账号暂停服务 | 需审核通过后方可恢复 |

##### 9.5.3.3 免扣分情形

| 情形 | 条件 | 所需证据 |
|---------|------|----------|
| 接驾点过远 | 及时联系客服说明情况 | 导航截图显示距离过远 |
| 提前报备迟到 | 发现可能迟到立即报备 | 导航截图、延误原因说明 |
| 乘客要求变更时间 | 及时告知客服无法配合 | 与乘客的沟通记录截图 |
| 航班延误 | 及时报备并提供航班信息 | 航班延误信息截图 |
| 不可抗力因素 | 自然灾害、交通管制等 | 现场照片、官方公告截图 |

##### 9.5.3.4 申诉与恢复机制

| 申诉类型 | 申诉流程 | 所需资料 |
|---------|------|----------|
| 误扣分申诉 | 1. 联系客服提交申诉<br>2. 提供相关证据<br>3. 等待审核结果 | 证明材料、订单号、时间记录 |
| 评分恢复申请 | 1. 连续15天无新增扣分<br>2. 提交评分恢复申请<br>3. 提供服务改进说明 | 近期服务记录、改进计划 |
| 账号恢复申请 | 1. 账号被暂停后提交申请<br>2. 参加必要的培训课程<br>3. 通过审核后恢复账号 | 培训证明、改进承诺书 |

#### 9.5.4 订单取消机制

##### 9.5.4.1 自动取消机制

| 情形 | 自动取消规则 | 扣分标准 |
|---------|------|----------|
| 接单后未及时点击“On The Way” | 接单后1小时内未点击“On The Way”，系统发送警告消息，15分钟后仍未操作则自动取消 | 扣除内部评分：1分 |
| 点击“On The Way”后未及时点击“到达” | 接机：航班到达后15分钟内未点击“到达”，系统发送警告，再次提醒后5分钟仍未操作则自动取消<br>送机：预计接驾时间30分钟前未点击“到达”，系统发送警告，再次提醒后5分钟仍未操作则自动取消 | 扣除内部评分：2分 |
| 订单开始前1小时内取消 | 订单接驾时间前1小时内取消订单 | 扣除内部评分：4分 |

##### 9.5.4.2 自主取消标准与流程

| 取消时间点 | 操作流程 | 扣分情况 | 备注 |
|---------|----------|----------|------|
| 订单开始前6小时以上 | 1. 点击“取消”按钮<br>2. 确认取消 | 扣1分 | 常规取消 |
| 订单开始前1-6小时 | 1. 点击“取消”按钮<br>2. 提交取消原因说明<br>3. 等待审核 | 扣2分 | 特殊情况可申请无责取消 |
| 订单开始前1小时内 | 1. 立即联系客服<br>2. 提交相关证明<br>3. 等待审核 | 扣4分 | 审核通过后可补分 |

##### 9.5.4.3 无责取消申请条件

| 申请情形 | 所需证据 | 审核标准 |
|---------|------|----------|
| 乘客要求变更时间 | 与乘客的沟通记录截图 | 变更时间与司机行程冲突 |
| 接驾点过远 | 导航路线截图，显示距离过远 | 单程超过50公里或时间超过1小时 |
| 航班延误 | 航班延误信息截图 | 航班延误超过2小时 |
| 车辆紧急故障 | 故障照片、维修记录 | 故障影响行车安全 |
| 突发疾病或个人紧急情况 | 医疗证明或其他相关证明 | 情况真实且紧急 |

### 9.6 工作效率提升

#### 9.6.1 订单管理技巧

* **仓细查看订单时间与路线**：避免订单冲突
* **保持准时及良好服务**：提高评分
* **异常记录与证据保存**：便于后续申诉或证明

#### 9.6.2 服务质量提升

* **专业态度**：保持礼谐、耐心、专业的服务态度
* **车内环境**：保持车内清洁整洁，提供舒适的乘车体验
* **安全驾驶**：始终将乘客安全放在首位，遵守交通规则

### 9.7 特定OTA渠道流程

#### 9.7.1 不同OTA渠道的特殊处理流程

* **携程渠道**：
  * GMH 订单的extra requirement 会显示需要使用携程司机端
  * 需要同时操作 GMH app 以及 携程司导端
  * 使用携程司机端IM联系客人，同时在GMH应用内记录关键服务信息
  * 订单资料以携程司机端展示为主，但必须与GMH系统信息保持一致
  * 携程渠道接机服务免费等待时间为90分钟，超过时间按标准超时费率计费
  * 等待60分钟客人无回应时，需在携程IM中报备并通知GMH客服
  * 如等待超过90分钟，需客服介入才能离场

* **飞猪/Jing Ge/Chong Dealer渠道**：
  * GMH 订单的extra requirement 会显示需要入微信群
  * 针对中国客户的特殊流程
  * 需在GMH聊天中扫描二维码入群
  * 如无二维码，立即通知客服获取协助
  * 入群后除了打招呼外，切勿回复其他内容
  * 完成订单后自行离群

#### 9.7.2 OTA渠道订单注意事项

* **信息确认**：第三方OTA订单信息可能与GMH系统不完全一致，需仔细核对
* **沟通记录**：所有与客人的沟通必须在对应OTA平台的官方渠道进行，保留完整记录
* **问题报备**：遇到任何问题，及时向GMH客服报备，同时在OTA平台留下记录
* **服务标准**：无论通过何种渠道接单，均需保持GMH的服务标准
